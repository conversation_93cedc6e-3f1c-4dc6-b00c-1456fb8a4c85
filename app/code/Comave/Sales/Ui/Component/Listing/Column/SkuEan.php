<?php
declare(strict_types=1);

namespace Comave\Sales\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\App\ResourceConnection;
use Comave\Sales\Model\EanProvider;


class SkuEan extends Column
{
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param ResourceConnection $resourceConnection
     * @param EanProvider $eanProvider
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface   $context,
        UiComponentFactory $uiComponentFactory,
        private readonly   ResourceConnection $resourceConnection,
        private readonly   EanProvider        $eanProvider,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare data source for SKU/EAN column.
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            $fieldName = $this->getData('name');
            foreach ($dataSource['data']['items'] as &$item) {
                $item[$fieldName] = $this->getSkuEanForOrder((int)$item['entity_id']);
            }
        }
        return $dataSource;
    }

    /**
     * Get SKU and EAN values for an order.
     *
     * @param int $orderId
     * @return string
     */
    private function getSkuEanForOrder(int $orderId): string
    {
        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()
            ->from(['soi' => $this->resourceConnection->getTableName('sales_order_item')], ['sku'])
            ->where('soi.order_id = ?', $orderId)
            ->where('soi.parent_item_id IS NULL');

        $skus = $connection->fetchCol($select);

        if (empty($skus)) {
            return '';
        }

        $eanValues = $this->getEanValues($skus);
        $result = [];
        foreach ($skus as $sku) {
            $eanValue = isset($eanValues[$sku]) && !empty($eanValues[$sku]) ? $eanValues[$sku] : '';
            if (!empty($eanValue)) {
                $result[] = $sku . ' / ' . $eanValue;
            } else {
                $result[] = $sku;
            }
        }

        return implode('<br/>', $result);
    }

    /**
     * Get EAN values for given SKUs.
     *
     * @param array $skus
     * @return array
     */
    private function getEanValues(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        $connection = $this->resourceConnection->getConnection();

        $eanAttrId = $this->eanProvider->getEanAttributeId();
        if (!$eanAttrId) {
            return [];
        }

        try {
            $varcharColumns = $connection->describeTable(
                $this->resourceConnection->getTableName('catalog_product_entity_varchar')
            );

            $varcharLinkField = isset($varcharColumns['row_id']) ? 'row_id' : 'entity_id';
            $useRowId          = $this->eanProvider->hasRowIdColumn();
            $mainLinkField = $useRowId ? 'row_id' : 'entity_id';

            $select = $connection->select()
                ->from(['cpe' => $this->resourceConnection->getTableName('catalog_product_entity')], [$mainLinkField, 'sku'])
                ->where('cpe.sku IN (?)', $skus);

            $productData = $connection->fetchPairs($select);
            if (empty($productData)) {
                return [];
            }

            $select = $connection->select()
                ->from(['cpev' => $this->resourceConnection->getTableName('catalog_product_entity_varchar')], [$varcharLinkField, 'value'])
                ->where('cpev.' . $varcharLinkField . ' IN (?)', array_keys($productData))
                ->where('cpev.attribute_id = ?', $eanAttrId)
                ->where('cpev.store_id = 0');

            $eanData = $connection->fetchPairs($select);

            $result = [];
            foreach ($productData as $linkId => $sku) {
                if (isset($eanData[$linkId])) {
                    $result[$sku] = $eanData[$linkId];
                }
            }

            return $result;

        } catch (\Exception $e) {
            return [];
        }
    }
}
