<?php
declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;
use Comave\Sales\Model\EanProvider;

class OrderGridSkuEanSearchPlugin
{
    public function __construct(
        private readonly EanProvider $eanProvider
    ) {}

    /**
     * Intercept addFieldToFilter to enable SKU/EAN searching.
     *
     * @param Collection $collection
     * @param \Closure $proceed
     * @param string $field
     * @param mixed $condition
     * @return Collection
     */
    public function aroundAddFieldToFilter(
        Collection $collection,
        \Closure $proceed,
        $field,
        $condition = null
    ) {
        if ($field !== 'sku_ean') {
            return $proceed($field, $condition);
        }

        if (!$collection->getFlag('sku_ean_filter_added')) {
            $collection->setFlag('sku_ean_filter_added', true);
            $this->addSkuEanJoinsForFilter($collection);
        }

        $searchValue = is_array($condition) && isset($condition['like']) 
            ? trim($condition['like'], '%') 
            : (string)$condition;

        $eanAttrId = $this->eanProvider->getEanAttributeId($collection);
        
        $orConditions = [];
        $orConditions[] = $collection->getConnection()->quoteInto('soi.sku = ?', $searchValue);
        
        if ($eanAttrId) {
            $orConditions[] = $collection->getConnection()->quoteInto('ean_attr.value = ?', $searchValue);
        }
        
        $collection->getSelect()->where('(' . implode(' OR ', $orConditions) . ')');

        return $collection;
    }

    /**
     * Add SKU/EAN joins for filtering only.
     *
     * @param Collection $collection
     * @return void
     */
    private function addSkuEanJoinsForFilter(Collection $collection): void
    {
        $collection->getSelect()->joinLeft(
            ['soi' => $collection->getTable('sales_order_item')],
            'main_table.entity_id = soi.order_id',
            []
        );

        $collection->getSelect()->joinLeft(
            ['cpe' => $collection->getTable('catalog_product_entity')],
            'soi.product_id = cpe.entity_id',
            []
        );

        $eanAttrId = $this->eanProvider->getEanAttributeId($collection);
        if ($eanAttrId) {
            $joinCondition = $this->eanProvider->hasRowIdColumn($collection)
                ? 'cpe.row_id = ean_attr.row_id'
                : 'cpe.entity_id = ean_attr.entity_id';
            $joinCondition .= ' AND ean_attr.attribute_id = ' . $eanAttrId . ' AND ean_attr.store_id = 0';

            $collection->getSelect()->joinLeft(
                ['ean_attr' => $collection->getTable('catalog_product_entity_varchar')],
                $joinCondition,
                []
            );
        }

        $collection->getSelect()->group('main_table.entity_id');
    }
}
