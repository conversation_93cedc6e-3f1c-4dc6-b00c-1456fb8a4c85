<?php
declare(strict_types=1);

namespace Comave\Sales\Model;

use Magento\Framework\App\ResourceConnection;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;

class EanProvider
{
    public function __construct(
        private readonly ResourceConnection $resourceConnection
    ) {}

    /**
     * @param Collection|null $collection
     *   If passed, we use its connection; otherwise fallback to default.
     */
    public function getEanAttributeId(?Collection $collection = null): ?int
    {
        $connection = $collection
            ? $collection->getConnection()
            : $this->resourceConnection->getConnection();

        try {
            $select = $connection->select()
                ->from(
                    ['ea' => $connection->getTableName('eav_attribute')],
                    ['attribute_id']
                )
                ->where('ea.entity_type_id = ?', 4)
                ->where('ea.attribute_code = ?', 'ean');

            $attributeId = $connection->fetchOne($select);
            return $attributeId ? (int)$attributeId : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function hasRowIdColumn(?Collection $collection = null): bool
    {
        $connection = $collection
            ? $collection->getConnection()
            : $this->resourceConnection->getConnection();

        try {
            $columns = $connection->describeTable(
                $connection->getTableName('catalog_product_entity')
            );
            return isset($columns['row_id']);
        } catch (\Exception $e) {
            return false;
        }
    }
}
